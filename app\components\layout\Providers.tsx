'use client';

import { SessionProvider } from 'next-auth/react';
import { PlayerProvider } from '../../contexts/PlayerContext';

interface ProvidersProps {
  children: React.ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  return (
    <PlayerProvider>
      <SessionProvider
        refetchInterval={5 * 60} // Refetch session every 5 minutes instead of default
        refetchOnWindowFocus={false} // Don't refetch when window gains focus
      >
        {children}
      </SessionProvider>
    </PlayerProvider>
  );
}