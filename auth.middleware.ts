import NextAuth from "next-auth";
import type { NextAuthConfig } from "next-auth";

// Notice this is only an object, not a full Auth.js instance
// This configuration is for Edge Functions and doesn't include database adapters
export default {
  providers: [], // No providers needed for middleware - just authentication checks
  callbacks: {
    authorized: async ({ auth }) => {
      // For middleware, we just need to check if user is authenticated
      // The actual authentication logic happens in the main auth.config.ts
      return !!auth;
    },
  },
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/auth/login",
  },
  secret: process.env.NEXTAUTH_SECRET,
  trustHost: true,
} satisfies NextAuthConfig;