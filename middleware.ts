import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define public routes that don't require authentication
const publicRoutes = [
  '/auth/login',
  '/auth/register',
  '/api/auth',
  '/api/auth/register',
  '/api/auth/callback',
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // Allow static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // Check for authentication by looking for the session cookie
  const sessionToken = request.cookies.get('next-auth.session-token')?.value ||
                       request.cookies.get('__Secure-next-auth.session-token')?.value;

  console.log('Middleware - Path:', pathname);
  console.log('Middleware - Session token exists:', !!sessionToken);

  // If no session token and trying to access a protected route
  if (!sessionToken && pathname !== '/auth/login') {
    const loginUrl = new URL('/auth/login', request.url);
    loginUrl.searchParams.set('callbackUrl', pathname);
    console.log('Middleware - Redirecting to login:', loginUrl.toString());
    return NextResponse.redirect(loginUrl);
  }

  // If session token exists and trying to access login page, redirect to home
  if (sessionToken && pathname === '/auth/login') {
    console.log('Middleware - Redirecting authenticated user from login to home');
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Allow all other requests
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (authentication API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
};
