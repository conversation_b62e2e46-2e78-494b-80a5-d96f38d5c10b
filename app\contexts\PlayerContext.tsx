'use client';

import React, { createContext, useContext, useState, useRef, useEffect } from 'react';

interface Song {
  _id: string;
  title: string;
  author: string;
  coverImageUrl: string;
  audioUrl: string;
  uploadedBy?: {
    username?: string;
    firstName?: string;
    lastName?: string;
  };
  createdAt: string;
}

interface PlayerContextType {
  currentSong: Song | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  play: (song: Song) => void;
  pause: () => void;
  togglePlay: () => void;
  setVolume: (volume: number) => void;
  seek: (time: number) => void;
  next: () => void;
  previous: () => void;
  playlist: Song[];
  setPlaylist: (songs: Song[]) => void;
}

const PlayerContext = createContext<PlayerContextType | undefined>(undefined);

export const usePlayer = () => {
  const context = useContext(PlayerContext);
  if (!context) {
    throw new Error('usePlayer must be used within a PlayerProvider');
  }
  return context;
};

interface PlayerProviderProps {
  children: React.ReactNode;
}

export const PlayerProvider: React.FC<PlayerProviderProps> = ({ children }) => {
  // Initialize with default state to prevent hydration mismatch
  const [currentSong, setCurrentSong] = useState<Song | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(0.7);
  const [playlist, setPlaylist] = useState<Song[]>([]);
  
  // Keys for local storage
  const PLAYER_STATE_KEY = 'melodix-player-state';
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const fadeIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const playPromiseRef = useRef<Promise<void> | null>(null);
  
  // Fade constants (in milliseconds)
  const FADE_DURATION = 250; // 200ms fade in/out (faster)
  const FADE_STEP = 0.05; // Volume change per step

  // Load player state from local storage on mount (client-side only)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedState = localStorage.getItem(PLAYER_STATE_KEY);
        if (savedState) {
          const { song, time, vol } = JSON.parse(savedState);

          // Restore state on client-side
          if (song) {
            setCurrentSong(song);
            setCurrentTime(time || 0);
            setVolume(vol || 0.7);

            // Set up the audio with saved position but don't play
            setTimeout(() => {
              if (audioRef.current) {
                audioRef.current.src = song.audioUrl;
                audioRef.current.currentTime = time || 0;
                audioRef.current.volume = vol || 0.7;
              }
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error loading player state from localStorage:', error);
      }
    }
  }, []);
  
  // Save player state to local storage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const stateToSave = {
          song: currentSong,
          time: currentTime,
          playing: isPlaying,
          vol: volume
        };
        localStorage.setItem(PLAYER_STATE_KEY, JSON.stringify(stateToSave));
      } catch (error) {
        console.error('Error saving player state to localStorage:', error);
      }
    }
  }, [currentSong, currentTime, isPlaying, volume]);

  // Update volume when it changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  // Fade in effect
  const fadeIn = () => {
    if (fadeIntervalRef.current) {
      clearInterval(fadeIntervalRef.current);
    }
    
    let currentVolume = 0;
    const targetVolume = volume;
    const startTime = Date.now();
    
    fadeIntervalRef.current = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / FADE_DURATION, 1);
      
      // Calculate current volume with easing function for smoother transition
      currentVolume = targetVolume * progress;
      
      if (audioRef.current) {
        audioRef.current.volume = currentVolume;
      }
      
      if (progress >= 1) {
        if (fadeIntervalRef.current) {
          clearInterval(fadeIntervalRef.current);
          fadeIntervalRef.current = null;
        }
      }
    }, FADE_STEP * 20); // Update every 20ms for smooth transition
  };

  // Fade out effect
  const fadeOut = (callback?: () => void) => {
    if (fadeIntervalRef.current) {
      clearInterval(fadeIntervalRef.current);
    }
    
    const startVolume = audioRef.current ? audioRef.current.volume : 0;
    const startTime = Date.now();
    
    fadeIntervalRef.current = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / FADE_DURATION, 1);
      
      // Calculate current volume with easing function
      const currentVolume = startVolume * (1 - progress);
      
      if (audioRef.current) {
        audioRef.current.volume = currentVolume;
      }
      
      if (progress >= 1) {
        if (fadeIntervalRef.current) {
          clearInterval(fadeIntervalRef.current);
          fadeIntervalRef.current = null;
        }
        
        // Execute callback after fade is complete
        if (callback) {
          callback();
        }
      }
    }, FADE_STEP * 20); // Update every 20ms for smooth transition
  };


  const play = async (song: Song) => {
    if (audioRef.current) {
      try {
        // Wait for any existing play promise to resolve/reject before starting new one
        if (playPromiseRef.current) {
          try {
            await playPromiseRef.current;
          } catch (error) {
            // Ignore errors from previous play attempts
            console.log('Previous play promise rejected, continuing with new play');
          }
        }

        audioRef.current.src = song.audioUrl;

        // Check if we're resuming the same song and restore position
        if (currentSong && currentSong._id === song._id && currentTime > 0) {
          audioRef.current.currentTime = currentTime;
        } else {
          // Reset position if it's a different song
          audioRef.current.currentTime = 0;
        }

        // Set initial volume to 0 for fade in effect
        audioRef.current.volume = 0;

        // Store the play promise to handle interruptions
        playPromiseRef.current = audioRef.current.play();
        await playPromiseRef.current;

        setCurrentSong(song);
        setIsPlaying(true);

        // Start fade in effect
        fadeIn();
      } catch (error) {
        console.error('Error playing audio:', error);
        setIsPlaying(false);
        // Clear the promise reference on error
        playPromiseRef.current = null;
      }
    }
  };

  const pause = async () => {
    if (audioRef.current) {
      try {
        // Wait for any pending play promise to resolve before pausing
        if (playPromiseRef.current) {
          try {
            await playPromiseRef.current;
          } catch (error) {
            // Play was already interrupted, safe to continue
            console.log('Play promise was rejected, safe to pause');
          }
          playPromiseRef.current = null;
        }

        // Fade out before pausing
        fadeOut(() => {
          audioRef.current?.pause();
          setIsPlaying(false);
        });
      } catch (error) {
        console.error('Error pausing audio:', error);
        setIsPlaying(false);
      }
    }
  };

  const togglePlay = async () => {
    if (!currentSong) return;

    if (isPlaying) {
      await pause();
    } else {
      if (audioRef.current) {
        try {
          // Wait for any existing play promise to resolve/reject
          if (playPromiseRef.current) {
            try {
              await playPromiseRef.current;
            } catch (error) {
              console.log('Previous play promise rejected, continuing with toggle');
            }
          }

          audioRef.current.src = currentSong.audioUrl;

          // Restore the saved playback position when resuming
          if (currentTime > 0) {
            audioRef.current.currentTime = currentTime;
          }

          // Set initial volume to 0 for fade in effect
          audioRef.current.volume = 0;

          // Store the play promise
          playPromiseRef.current = audioRef.current.play();
          await playPromiseRef.current;

          setIsPlaying(true);

          // Start fade in effect
          fadeIn();
        } catch (error) {
          console.error('Error toggling play:', error);
          setIsPlaying(false);
          playPromiseRef.current = null;
        }
      }
    }
  };

  // Initialize audio element
  useEffect(() => {
    if (typeof window !== 'undefined') {
      audioRef.current = new Audio();
      
      const handleTimeUpdate = () => {
        if (audioRef.current) {
          setCurrentTime(audioRef.current.currentTime);
        }
      };
      
      const handleLoadedMetadata = () => {
        if (audioRef.current) {
          setDuration(audioRef.current.duration);
        }
      };
      
      const handleEnded = async () => {
        setIsPlaying(false);
        // Auto-play next song if available
        if (playlist.length > 0) {
          const currentIndex = currentSong ? playlist.findIndex(song => song._id === currentSong._id) : -1;
          if (currentIndex < playlist.length - 1) {
            await play(playlist[currentIndex + 1]);
          }
        }
      };
      
      const handleError = (e: ErrorEvent) => {
        console.error('Audio error:', e);
        setIsPlaying(false);
      };
      
      if (audioRef.current) {
        audioRef.current.addEventListener('timeupdate', handleTimeUpdate);
        audioRef.current.addEventListener('loadedmetadata', handleLoadedMetadata);
        audioRef.current.addEventListener('ended', handleEnded);
        audioRef.current.addEventListener('error', handleError);
        audioRef.current.volume = volume;
      }
      
      return () => {
        if (audioRef.current) {
          audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);
          audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);
          audioRef.current.removeEventListener('ended', handleEnded);
          audioRef.current.removeEventListener('error', handleError);
          audioRef.current.pause();
          audioRef.current.src = '';
        }
        
        // Clear any ongoing fade effects
        if (fadeIntervalRef.current) {
          clearInterval(fadeIntervalRef.current);
          fadeIntervalRef.current = null;
        }
      };
    }
  }, [play, playlist, currentSong, volume]);

  // Add keyboard event listener for space bar
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check if space bar is pressed
      if (e.code === 'Space') {
        // Prevent default behavior (scrolling) when space is pressed
        e.preventDefault();
        togglePlay();
      }
    };

    // Add event listener
    window.addEventListener('keydown', handleKeyDown);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [togglePlay]);

  const seek = (time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const next = () => {
    if (!currentSong || playlist.length === 0) return;

    const currentIndex = playlist.findIndex(song => song._id === currentSong._id);
    if (currentIndex < playlist.length - 1) {
      // Fade out before switching to next song
      fadeOut(async () => {
        await play(playlist[currentIndex + 1]);
      });
    }
  };

  const previous = async () => {
    if (!currentSong || playlist.length === 0) return;

    const currentIndex = playlist.findIndex(song => song._id === currentSong._id);
    if (currentIndex > 0) {
      // Fade out before switching to previous song
      fadeOut(async () => {
        await play(playlist[currentIndex - 1]);
      });
    } else {
      // If at first song, restart it with fade in
      if (audioRef.current) {
        try {
          // Wait for any existing play promise
          if (playPromiseRef.current) {
            try {
              await playPromiseRef.current;
            } catch (error) {
              console.log('Previous play promise rejected, continuing with restart');
            }
          }

          audioRef.current.currentTime = 0;
          audioRef.current.volume = 0; // Reset volume for fade in

          playPromiseRef.current = audioRef.current.play();
          await playPromiseRef.current;

          setIsPlaying(true);
          fadeIn(); // Apply fade in effect
        } catch (error) {
          console.error('Error restarting song:', error);
          setIsPlaying(false);
          playPromiseRef.current = null;
        }
      }
    }
  };

  const value: PlayerContextType = {
    currentSong,
    isPlaying,
    currentTime,
    duration,
    volume,
    play,
    pause,
    togglePlay,
    setVolume,
    seek,
    next,
    previous,
    playlist,
    setPlaylist,
  };

  return (
    <PlayerContext.Provider value={value}>
      {children}
    </PlayerContext.Provider>
  );
};